import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/paymentgateway/paymentwebview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';

class DabbaWalletPage extends StatefulWidget {
  const DabbaWalletPage({super.key});

  @override
  State<DabbaWalletPage> createState() => _DabbaWalletPageState();
}

class _DabbaWalletPageState extends State<DabbaWalletPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  @override
  void initState() {
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _loadInitialData();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
    super.initState();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _scrollListener() {
    if (!_isLoadingMore &&
        _hasMoreData &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadInitialData() {
    context.read<AccountBloc>().add(WalletTransactionListEvent('', page: 1));
  }

  void _loadMore() {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    context.read<AccountBloc>().add(
          WalletTransactionListEvent(
            '',
            page: _currentPage + 1,
          ),
        );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        scrolledUnderElevation: 0,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is WalletTransactionLoadingSuccess) {
            setState(() {
              _isLoadingMore = false;
              _currentPage = Initializer.walletTransactionListModel.data
                      ?.pagination?.currentPage ??
                  _currentPage;

              // Update hasMoreData based on pagination data
              final pagination =
                  Initializer.walletTransactionListModel.data?.pagination;
              if (pagination != null) {
                _hasMoreData = pagination.currentPage! < pagination.totalPages!;
              } else {
                _hasMoreData = false;
              }
            });
          } else if (state is WalletTransactionLoadingAFailed) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        },
        builder: (context, state) {
          if (state is WalletTransactionListLoading) {
            // return buildShimmer();
          } else if (state is WalletTransactionLoadingAFailed) {
            return Center(
              child: Text(
                'Error loading transactions',
                style: TextStyle(color: Colors.red),
              ),
            );
          } else if (state is WalletTransactionLoadingSuccess) {}
          return Initializer.walletTransactionListModel.data == null
              ? buildShimmer()
              : SingleChildScrollView(
                  controller: _scrollController,
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 0,
                        bottom: sixteen,
                        left: eighteen,
                        right: eighteen),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Payments heading
                        Text(
                          'Payments',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: eighteen,
                            height: 1.24,
                            letterSpacing: -1,
                            color: Colors.black,
                          ),
                        ),

                        SizedBox(height: eighteen),

                        // White container
                        Container(
                          width: double.infinity,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: sixteen,
                                bottom: sixteen,
                                right: sixteen,
                                left: sixteen),
                            child: Column(
                              children: [
                                // Image with overlay text
                                Stack(
                                  children: [
                                    Container(
                                      height: ten * 20.6,
                                      width: double.infinity,
                                      decoration: const BoxDecoration(
                                        image: DecorationImage(
                                          image: AssetImage('assets/dbbg.png'),
                                          fit: BoxFit.fill,
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      top: sixteen,
                                      left: sixteen,
                                      child: Text(
                                        'Eatro Wallet',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w600,
                                          fontSize: sixteen,
                                          height: 1.24,
                                          letterSpacing: 0,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: sixteen,
                                      left: sixteen,
                                      child: Text(
                                        '\$${Initializer.walletTransactionListModel.data!.balance?.toStringAsFixed(2) ?? '0.00'}',
                                        style: TextStyle(
                                          fontFamily: 'Suisse Intl',
                                          fontWeight: FontWeight.w600,
                                          fontSize: twentyFour,
                                          height: 1.20,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                SizedBox(height: twelve),

                                // Cash in button
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 0.0),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            double? selectedAmount;
                                            final amounts = [
                                              100.0,
                                              200.0,
                                              500.0
                                            ];
                                            final TextEditingController
                                                controller =
                                                TextEditingController();
                                            return StatefulBuilder(
                                              builder: (context, setState) {
                                                final size =
                                                    MediaQuery.of(context).size;
                                                final isTablet =
                                                    size.width >= 600;

                                                return Dialog(
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            twenty),
                                                  ),
                                                  child: Container(
                                                    constraints: BoxConstraints(
                                                      maxWidth: isTablet
                                                          ? 500
                                                          : size.width * 0.9,
                                                      maxHeight:
                                                          size.height * 0.8,
                                                    ),
                                                    child:
                                                        SingleChildScrollView(
                                                      child: Padding(
                                                        padding: EdgeInsets.all(
                                                            isTablet
                                                                ? twentyFour
                                                                : twenty),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            // Title
                                                            Row(
                                                              children: [
                                                                Icon(
                                                                  Icons
                                                                      .account_balance_wallet_rounded,
                                                                  color: Colors
                                                                      .black87,
                                                                  size: isTablet
                                                                      ? twentyFour
                                                                      : twenty,
                                                                ),
                                                                SizedBox(
                                                                    width:
                                                                        sixteen /
                                                                            2),
                                                                Expanded(
                                                                  child: Text(
                                                                    'Add Money',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize: isTablet
                                                                          ? twentyFour
                                                                          : twenty,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            SizedBox(
                                                                height: twenty),

                                                            // Amount input field
                                                            Container(
                                                              width: double
                                                                  .infinity,
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: const Color(
                                                                    0xFFF6F3EC),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            twelve),
                                                              ),
                                                              child: TextField(
                                                                controller:
                                                                    controller,
                                                                keyboardType:
                                                                    const TextInputType
                                                                        .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                                style:
                                                                    TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  fontSize: isTablet
                                                                      ? eighteen
                                                                      : sixteen,
                                                                ),
                                                                decoration:
                                                                    InputDecoration(
                                                                  prefixIcon: Icon(
                                                                      Icons
                                                                          .attach_money,
                                                                      size: isTablet
                                                                          ? twentyFour
                                                                          : twenty),
                                                                  border:
                                                                      InputBorder
                                                                          .none,
                                                                  contentPadding: EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          sixteen,
                                                                      vertical: isTablet
                                                                          ? eighteen
                                                                          : sixteen),
                                                                  hintText:
                                                                      'Enter amount',
                                                                  hintStyle: TextStyle(
                                                                      fontSize: isTablet
                                                                          ? eighteen
                                                                          : sixteen,
                                                                      color: Colors
                                                                              .grey[
                                                                          600]),
                                                                  isDense: true,
                                                                ),
                                                                onChanged:
                                                                    (value) {
                                                                  double? val =
                                                                      double.tryParse(
                                                                          value);
                                                                  // Limit to 99999.99
                                                                  if (val !=
                                                                          null &&
                                                                      val >
                                                                          99999.99) {
                                                                    val =
                                                                        99999.99;
                                                                    controller
                                                                            .text =
                                                                        val.toStringAsFixed(
                                                                            2);
                                                                    controller
                                                                            .selection =
                                                                        TextSelection
                                                                            .fromPosition(
                                                                      TextPosition(
                                                                          offset: controller
                                                                              .text
                                                                              .length),
                                                                    );
                                                                  }
                                                                  setState(() {
                                                                    selectedAmount =
                                                                        val;
                                                                  });
                                                                },
                                                              ),
                                                            ),

                                                            SizedBox(
                                                                height: twenty),

                                                            // Quick amounts label
                                                            Text(
                                                              'Quick Amounts',
                                                              style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                color: Colors
                                                                    .grey[700],
                                                                fontSize: isTablet
                                                                    ? sixteen
                                                                    : forteen,
                                                              ),
                                                            ),

                                                            SizedBox(
                                                                height: twelve),

                                                            // Quick amounts chips
                                                            Wrap(
                                                              spacing: isTablet
                                                                  ? sixteen
                                                                  : twelve,
                                                              runSpacing:
                                                                  isTablet
                                                                      ? twelve
                                                                      : ten,
                                                              children: amounts
                                                                  .map(
                                                                      (amount) {
                                                                final isSelected =
                                                                    selectedAmount ==
                                                                        amount;
                                                                return ChoiceChip(
                                                                  label: Text(
                                                                      '\$${amount.toInt()}'),
                                                                  selected:
                                                                      isSelected,
                                                                  selectedColor:
                                                                      Colors
                                                                          .black,
                                                                  labelStyle:
                                                                      TextStyle(
                                                                    color: isSelected
                                                                        ? Colors
                                                                            .white
                                                                        : Colors
                                                                            .black,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    fontSize: isTablet
                                                                        ? forteen
                                                                        : twelve,
                                                                  ),
                                                                  backgroundColor:
                                                                      const Color(
                                                                          0xFFF6F3EC),
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            ten),
                                                                    side:
                                                                        BorderSide(
                                                                      color: isSelected
                                                                          ? Colors
                                                                              .black
                                                                          : Colors
                                                                              .grey
                                                                              .shade300,
                                                                      width: 1,
                                                                    ),
                                                                  ),
                                                                  showCheckmark:
                                                                      false,
                                                                  onSelected:
                                                                      (_) {
                                                                    setState(
                                                                        () {
                                                                      selectedAmount =
                                                                          amount;
                                                                      controller
                                                                              .text =
                                                                          amount
                                                                              .toStringAsFixed(2);
                                                                    });
                                                                  },
                                                                );
                                                              }).toList(),
                                                            ),

                                                            SizedBox(
                                                                height:
                                                                    twentyFour),

                                                            // Action buttons
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .end,
                                                              children: [
                                                                TextButton(
                                                                  onPressed: () =>
                                                                      Navigator.pop(
                                                                          context),
                                                                  style: TextButton
                                                                      .styleFrom(
                                                                    foregroundColor:
                                                                        Colors
                                                                            .black,
                                                                    padding:
                                                                        EdgeInsets
                                                                            .symmetric(
                                                                      horizontal: isTablet
                                                                          ? twenty
                                                                          : sixteen,
                                                                      vertical: isTablet
                                                                          ? twelve
                                                                          : ten,
                                                                    ),
                                                                  ),
                                                                  child: Text(
                                                                    'Cancel',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize: isTablet
                                                                          ? sixteen
                                                                          : forteen,
                                                                    ),
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width:
                                                                        twelve),
                                                                BlocListener<
                                                                    AccountBloc,
                                                                    AccountState>(
                                                                  listener:
                                                                      (context,
                                                                          state) {
                                                                    if (state
                                                                        is WalletDepositSuccess) {
                                                                      print(
                                                                          'WalletDepositSuccess received');
                                                                      print(
                                                                          'Payment URL: ${state.paymentUrl}');

                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();

                                                                      if (state.paymentUrl !=
                                                                              null &&
                                                                          state
                                                                              .paymentUrl!
                                                                              .isNotEmpty) {
                                                                        print(
                                                                            'Navigating to PaymentWebView with URL: ${state.paymentUrl}');
                                                                        // Navigate to payment gateway
                                                                        Navigator
                                                                            .push(
                                                                          context,
                                                                          MaterialPageRoute(
                                                                            builder: (context) =>
                                                                                PaymentWebView(
                                                                              paymentUrl: state.paymentUrl!,
                                                                              onPaymentComplete: () {
                                                                                _showPaymentSuccessDialog();
                                                                              },
                                                                              onPaymentCancelled: () {
                                                                                _showPaymentFailedDialog();
                                                                              },
                                                                            ),
                                                                          ),
                                                                        );
                                                                      } else {
                                                                        print(
                                                                            'No payment URL found, showing direct success');
                                                                      }
                                                                      //  else {
                                                                      //   // Direct deposit success (no payment gateway)
                                                                      //   ScaffoldMessenger.of(context)
                                                                      //       .showSnackBar(
                                                                      //     const SnackBar(
                                                                      //       content:
                                                                      //           Text('Deposit successful!'),
                                                                      //       duration:
                                                                      //           Duration(seconds: 2),
                                                                      //     ),
                                                                      //   );
                                                                      // }
                                                                    }
                                                                  },
                                                                  child: BlocBuilder<
                                                                      AccountBloc,
                                                                      AccountState>(
                                                                    builder:
                                                                        (context,
                                                                            state) {
                                                                      final isLoading =
                                                                          state
                                                                              is WalletDepositLoading;
                                                                      return ElevatedButton
                                                                          .icon(
                                                                        icon:
                                                                            Icon(
                                                                          Icons
                                                                              .arrow_upward,
                                                                          size: isTablet
                                                                              ? twenty
                                                                              : eighteen,
                                                                        ),
                                                                        label: isLoading
                                                                            ? SizedBox(
                                                                                width: isTablet ? twenty : eighteen,
                                                                                height: isTablet ? twenty : eighteen,
                                                                                child: Padding(
                                                                                  padding: EdgeInsets.all(2.0),
                                                                                  child: CircularProgressIndicator(
                                                                                    strokeWidth: 2,
                                                                                    color: Colors.white,
                                                                                  ),
                                                                                ),
                                                                              )
                                                                            : Text(
                                                                                'Deposit',
                                                                                style: TextStyle(
                                                                                  fontSize: isTablet ? sixteen : forteen,
                                                                                ),
                                                                              ),
                                                                        style: ElevatedButton
                                                                            .styleFrom(
                                                                          backgroundColor:
                                                                              Colors.black,
                                                                          foregroundColor:
                                                                              Colors.white,
                                                                          shape:
                                                                              RoundedRectangleBorder(
                                                                            borderRadius:
                                                                                BorderRadius.circular(twelve),
                                                                          ),
                                                                          padding:
                                                                              EdgeInsets.symmetric(
                                                                            horizontal: isTablet
                                                                                ? twentyFour
                                                                                : eighteen,
                                                                            vertical: isTablet
                                                                                ? forteen
                                                                                : twelve,
                                                                          ),
                                                                        ),
                                                                        onPressed: (selectedAmount != null &&
                                                                                selectedAmount! > 0 &&
                                                                                selectedAmount! <= 99999.99 &&
                                                                                !isLoading)
                                                                            ? () {
                                                                                context.read<AccountBloc>().add(WalletDepositEvent(selectedAmount!));
                                                                              }
                                                                            : null,
                                                                      );
                                                                    },
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                        );
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.black,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(twenty * 2),
                                        ),
                                        padding: EdgeInsets.symmetric(
                                            vertical: forteen),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.add,
                                            color: Colors.white,
                                            size: sixteen,
                                          ),
                                          SizedBox(width: sixteen / 2),
                                          Text(
                                            'Cash in',
                                            style: TextStyle(
                                              fontFamily: 'Suisse Int\'l',
                                              fontWeight: FontWeight.w400,
                                              fontSize: twelve,
                                              height: 1.0,
                                              letterSpacing: 0.24,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(height: forteen),

                                // Withdraw button
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 0.0),
                                  child: SizedBox(
                                    width: double.infinity,
                                    child: OutlinedButton(
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            double? selectedAmount;
                                            final amounts = [
                                              100.0,
                                              200.0,
                                              500.0
                                            ];
                                            final TextEditingController
                                                controller =
                                                TextEditingController();
                                            return StatefulBuilder(
                                              builder: (context, setState) {
                                                final size =
                                                    MediaQuery.of(context).size;
                                                final isTablet =
                                                    size.width >= 600;

                                                return Dialog(
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            twenty),
                                                  ),
                                                  child: Container(
                                                    constraints: BoxConstraints(
                                                      maxWidth: isTablet
                                                          ? 500
                                                          : size.width * 0.9,
                                                      maxHeight:
                                                          size.height * 0.8,
                                                    ),
                                                    child:
                                                        SingleChildScrollView(
                                                      child: Padding(
                                                        padding: EdgeInsets.all(
                                                            isTablet
                                                                ? twentyFour
                                                                : twenty),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            // Title
                                                            Row(
                                                              children: [
                                                                Icon(
                                                                  Icons
                                                                      .account_balance_wallet_rounded,
                                                                  color: Colors
                                                                      .black87,
                                                                  size: isTablet
                                                                      ? twentyFour
                                                                      : twenty,
                                                                ),
                                                                SizedBox(
                                                                    width:
                                                                        sixteen /
                                                                            2),
                                                                Expanded(
                                                                  child: Text(
                                                                    'Withdraw Money',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize: isTablet
                                                                          ? twentyFour
                                                                          : twenty,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            SizedBox(
                                                                height: twenty),

                                                            // Amount input field
                                                            Container(
                                                              width: double
                                                                  .infinity,
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: const Color(
                                                                    0xFFF6F3EC),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            twelve),
                                                              ),
                                                              child: TextField(
                                                                controller:
                                                                    controller,
                                                                keyboardType:
                                                                    const TextInputType
                                                                        .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                                style:
                                                                    TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  fontSize: isTablet
                                                                      ? eighteen
                                                                      : sixteen,
                                                                ),
                                                                decoration:
                                                                    InputDecoration(
                                                                  prefixIcon: Icon(
                                                                      Icons
                                                                          .attach_money,
                                                                      size: isTablet
                                                                          ? twentyFour
                                                                          : twenty),
                                                                  border:
                                                                      InputBorder
                                                                          .none,
                                                                  contentPadding: EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          sixteen,
                                                                      vertical: isTablet
                                                                          ? eighteen
                                                                          : sixteen),
                                                                  hintText:
                                                                      'Enter amount',
                                                                  hintStyle: TextStyle(
                                                                      fontSize: isTablet
                                                                          ? eighteen
                                                                          : sixteen,
                                                                      color: Colors
                                                                              .grey[
                                                                          600]),
                                                                  isDense: true,
                                                                ),
                                                                onChanged:
                                                                    (value) {
                                                                  final val = double
                                                                      .tryParse(
                                                                          value);
                                                                  setState(() {
                                                                    selectedAmount =
                                                                        val;
                                                                  });
                                                                },
                                                              ),
                                                            ),

                                                            SizedBox(
                                                                height: twenty),

                                                            // Quick amounts label
                                                            Text(
                                                              'Quick Amounts',
                                                              style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                color: Colors
                                                                    .grey[700],
                                                                fontSize: isTablet
                                                                    ? sixteen
                                                                    : forteen,
                                                              ),
                                                            ),

                                                            SizedBox(
                                                                height: twelve),

                                                            // Quick amounts chips
                                                            Wrap(
                                                              spacing: isTablet
                                                                  ? sixteen
                                                                  : twelve,
                                                              runSpacing:
                                                                  isTablet
                                                                      ? twelve
                                                                      : ten,
                                                              children: amounts
                                                                  .map(
                                                                      (amount) {
                                                                final isSelected =
                                                                    selectedAmount ==
                                                                        amount;
                                                                return ChoiceChip(
                                                                  label: Text(
                                                                      '\$${amount.toInt()}'),
                                                                  selected:
                                                                      isSelected,
                                                                  selectedColor:
                                                                      Colors
                                                                          .black,
                                                                  labelStyle:
                                                                      TextStyle(
                                                                    color: isSelected
                                                                        ? Colors
                                                                            .white
                                                                        : Colors
                                                                            .black,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    fontSize: isTablet
                                                                        ? forteen
                                                                        : twelve,
                                                                  ),
                                                                  backgroundColor:
                                                                      const Color(
                                                                          0xFFF6F3EC),
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            ten),
                                                                    side:
                                                                        BorderSide(
                                                                      color: isSelected
                                                                          ? Colors
                                                                              .black
                                                                          : Colors
                                                                              .grey
                                                                              .shade300,
                                                                      width: 1,
                                                                    ),
                                                                  ),
                                                                  showCheckmark:
                                                                      false,
                                                                  onSelected:
                                                                      (_) {
                                                                    setState(
                                                                        () {
                                                                      selectedAmount =
                                                                          amount;
                                                                      controller
                                                                              .text =
                                                                          amount
                                                                              .toStringAsFixed(2);
                                                                    });
                                                                  },
                                                                );
                                                              }).toList(),
                                                            ),

                                                            SizedBox(
                                                                height:
                                                                    twentyFour),

                                                            // Action buttons
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .end,
                                                              children: [
                                                                TextButton(
                                                                  onPressed: () =>
                                                                      Navigator.pop(
                                                                          context),
                                                                  style: TextButton
                                                                      .styleFrom(
                                                                    foregroundColor:
                                                                        Colors
                                                                            .black,
                                                                    padding:
                                                                        EdgeInsets
                                                                            .symmetric(
                                                                      horizontal: isTablet
                                                                          ? twenty
                                                                          : sixteen,
                                                                      vertical: isTablet
                                                                          ? twelve
                                                                          : ten,
                                                                    ),
                                                                  ),
                                                                  child: Text(
                                                                    'Cancel',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize: isTablet
                                                                          ? sixteen
                                                                          : forteen,
                                                                    ),
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width:
                                                                        twelve),
                                                                BlocListener<
                                                                    AccountBloc,
                                                                    AccountState>(
                                                                  listener:
                                                                      (context,
                                                                          state) {
                                                                    if (state
                                                                        is WalletWithdrawSuccess) {
                                                                      Navigator.of(
                                                                              context)
                                                                          .pop();
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(
                                                                        const SnackBar(
                                                                          content:
                                                                              Text('Withdrawn successfully!'),
                                                                          duration:
                                                                              Duration(seconds: 2),
                                                                        ),
                                                                      );
                                                                    } else if (state
                                                                        is WalletWithdrawFailed) {
                                                                      ScaffoldMessenger.of(
                                                                              context)
                                                                          .showSnackBar(
                                                                        SnackBar(
                                                                          content:
                                                                              Text(state.message),
                                                                          duration:
                                                                              Duration(seconds: 3),
                                                                          backgroundColor:
                                                                              Colors.red,
                                                                        ),
                                                                      );
                                                                    }
                                                                  },
                                                                  child: BlocBuilder<
                                                                      AccountBloc,
                                                                      AccountState>(
                                                                    builder:
                                                                        (context,
                                                                            state) {
                                                                      final isLoading =
                                                                          state
                                                                              is WalletWithdrawLoading;
                                                                      return ElevatedButton
                                                                          .icon(
                                                                        icon:
                                                                            Icon(
                                                                          Icons
                                                                              .arrow_downward,
                                                                          size: isTablet
                                                                              ? twenty
                                                                              : eighteen,
                                                                        ),
                                                                        label: isLoading
                                                                            ? SizedBox(
                                                                                width: isTablet ? twenty : eighteen,
                                                                                height: isTablet ? twenty : eighteen,
                                                                                child: Padding(
                                                                                  padding: EdgeInsets.all(2.0),
                                                                                  child: CircularProgressIndicator(
                                                                                    strokeWidth: 2,
                                                                                    color: Colors.white,
                                                                                  ),
                                                                                ),
                                                                              )
                                                                            : Text(
                                                                                'Withdraw',
                                                                                style: TextStyle(
                                                                                  fontSize: isTablet ? sixteen : forteen,
                                                                                ),
                                                                              ),
                                                                        style: ElevatedButton
                                                                            .styleFrom(
                                                                          backgroundColor:
                                                                              Colors.black,
                                                                          foregroundColor:
                                                                              Colors.white,
                                                                          shape:
                                                                              RoundedRectangleBorder(
                                                                            borderRadius:
                                                                                BorderRadius.circular(twelve),
                                                                          ),
                                                                          padding:
                                                                              EdgeInsets.symmetric(
                                                                            horizontal: isTablet
                                                                                ? twentyFour
                                                                                : eighteen,
                                                                            vertical: isTablet
                                                                                ? forteen
                                                                                : twelve,
                                                                          ),
                                                                        ),
                                                                        onPressed: (selectedAmount != null &&
                                                                                selectedAmount! > 0 &&
                                                                                !isLoading)
                                                                            ? () {
                                                                                if (selectedAmount! <= (Initializer.walletTransactionListModel.data!.balance ?? 0)) {
                                                                                  context.read<AccountBloc>().add(WalletWithdrawEvent(selectedAmount!));
                                                                                } else {
                                                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                                                    const SnackBar(
                                                                                      content: Text('Insufficient balance for withdrawal'),
                                                                                      duration: Duration(seconds: 2),
                                                                                    ),
                                                                                  );
                                                                                }
                                                                              }
                                                                            : null,
                                                                      );
                                                                    },
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                        );
                                      },
                                      style: OutlinedButton.styleFrom(
                                        backgroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(twenty * 2),
                                        ),
                                        side: const BorderSide(
                                            color: Colors.black, width: 1),
                                        padding: EdgeInsets.symmetric(
                                            vertical: forteen),
                                      ),
                                      child: Text(
                                        'Withdraw',
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontSize: twelve,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(height: twentyFour),

                                // Transactions heading
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 0.0),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      'Transactions',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                        fontSize: sixteen,
                                        height: 1.24,
                                        letterSpacing: 0,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(height: ten),
                                if (Initializer.walletTransactionListModel.data
                                            ?.transactions !=
                                        null &&
                                    Initializer.walletTransactionListModel.data!
                                        .transactions!.isNotEmpty)
                                  ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: Initializer
                                        .walletTransactionListModel
                                        .data!
                                        .transactions!
                                        .length,
                                    separatorBuilder: (context, index) =>
                                        const Divider(
                                      color: Color(0xFFE1E3E6),
                                      thickness: 1,
                                      height: 1,
                                    ),
                                    itemBuilder: (context, index) {
                                      final tx = Initializer
                                          .walletTransactionListModel
                                          .data!
                                          .transactions![index];
                                      // You may need to adjust these fields based on your model
                                      final mainText =
                                          tx.title ?? 'Transaction';
                                      final timeText = tx.createdAt ?? '';
                                      final type = tx.transactionType ?? '';
                                      final price = tx.transactionType !=
                                              'DEBIT'
                                          ? '+\$${(tx.amount ?? 0).toStringAsFixed(2)}'
                                          : '-\$${(tx.amount ?? 0).abs().toStringAsFixed(2)}';

                                      String formattedTime =
                                          _formatTransactionTime(timeText);
                                      return _buildTransactionRow(
                                          mainText, formattedTime, price, type);
                                    },
                                  )
                                else
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: twentyFour),
                                    child: Center(
                                      child: Text(
                                        'No transactions found.',
                                        style: TextStyle(color: Colors.grey),
                                      ),
                                    ),
                                  ),

                                // Add loading indicator at the bottom when loading more
                                if (_isLoadingMore)
                                  Padding(
                                    padding: EdgeInsets.all(sixteen),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation<
                                                Color>(
                                            Color.fromARGB(255, 26, 26, 26)),
                                      ),
                                    ),
                                  ),

                                // // Show "No more transactions" text when no more data
                                // if (!_hasMoreData &&
                                //     !_isLoadingMore &&
                                //     _currentPage > 1 &&
                                //     Initializer
                                //             .walletTransactionListModel
                                //             .data
                                //             ?.transactions
                                //             ?.isNotEmpty ==
                                //         true)
                                //   FutureBuilder(
                                //     future: Future.delayed(
                                //         const Duration(seconds: 3)),
                                //     builder: (context, snapshot) {
                                //       if (snapshot.connectionState !=
                                //           ConnectionState.done) {
                                //         return Padding(
                                //           padding: EdgeInsets.all(sixteen),
                                //           child: Center(
                                //             child: Text(
                                //               'No more transactions to load',
                                //               style: TextStyle(
                                //                 color: Color(0xFF66696D),
                                //                 fontSize: forteen,
                                //               ),
                                //             ),
                                //           ),
                                //         );
                                //       } else {
                                //         return const SizedBox
                                //             .shrink(); // Hide after 3 seconds
                                //       }
                                //     },
                                //   ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
        },
      ),
    );
  }

  Widget buildShimmer() {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(
            top: 0, bottom: sixteen, left: eighteen, right: eighteen),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payments heading shimmer
            _buildShimmerBox(
              width: 80,
              height: 22,
              borderRadius: 4,
            ),

            SizedBox(height: eighteen),

            // White container with shimmer content
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Padding(
                padding: EdgeInsets.only(
                    top: sixteen,
                    bottom: sixteen,
                    right: sixteen,
                    left: sixteen),
                child: Column(
                  children: [
                    // Wallet card shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: 206,
                      borderRadius: sixteen / 2,
                    ),

                    SizedBox(height: twelve),

                    // Cash in button shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: ten * 4.8,
                      borderRadius: twenty * 2,
                    ),

                    SizedBox(height: forteen),

                    // Withdraw button shimmer
                    _buildShimmerBox(
                      width: double.infinity,
                      height: ten * 4.8,
                      borderRadius: twenty * 2,
                    ),

                    SizedBox(height: twentyFour),

                    // Transactions heading shimmer
                    Align(
                      alignment: Alignment.centerLeft,
                      child: _buildShimmerBox(
                        width: ten * 1.2,
                        height: twenty,
                        borderRadius: 4,
                      ),
                    ),

                    SizedBox(height: ten),

                    // Transaction rows shimmer
                    ...List.generate(
                        4,
                        (index) => Column(
                              children: [
                                _buildTransactionShimmerRow(),
                                if (index < 3)
                                  const Divider(
                                    color: Color(0xFFE1E3E6),
                                    thickness: 1,
                                    height: 1,
                                  ),
                              ],
                            )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerBox({
    required double width,
    required double height,
    double borderRadius = 8,
  }) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
              colors: const [
                Color(0xFFE0E0E0),
                Color(0xFFF5F5F5),
                Color(0xFFE0E0E0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTransactionShimmerRow() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: sixteen),
      child: Row(
        children: [
          // Transaction icon shimmer
          _buildShimmerBox(
            width: twentyFour,
            height: twentyFour,
            borderRadius: twelve,
          ),
          SizedBox(width: twelve),
          // Transaction details shimmer
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerBox(
                  width: double.infinity,
                  height: forteen,
                  borderRadius: 4,
                ),
                const SizedBox(height: 4),
                _buildShimmerBox(
                  width: 150,
                  height: twelve,
                  borderRadius: 4,
                ),
              ],
            ),
          ),
          SizedBox(width: twelve),
          // Amount shimmer
          _buildShimmerBox(
            width: 60,
            height: sixteen,
            borderRadius: 4,
          ),
        ],
      ),
    );
  }

  String _formatTransactionTime(String timeText) {
    // Try to parse ISO8601 or fallback to original string
    try {
      // Parse the datetime and convert to local time
      final dateTime = DateTime.parse(timeText).toLocal();
      // Format as "MMM d, yyyy, h:mm a"
      return "${_monthName(dateTime.month)} ${dateTime.day}, ${dateTime.year}, ${_formatHourMinute(dateTime)}";
    } catch (e) {
      return timeText;
    }
  }

  String _monthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return (month >= 1 && month <= 12) ? months[month - 1] : '';
  }

  String _formatHourMinute(DateTime dt) {
    int hour = dt.hour;
    final minute = dt.minute.toString().padLeft(2, '0');
    final ampm = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 == 0 ? 12 : hour % 12;
    return "$hour:$minute $ampm";
  }

  Widget _buildTransactionRow(
      String mainText, String timeText, String price, String type) {
    Color priceColor = price.startsWith('+')
        ? const Color(0xFF007A4D)
        : const Color(0xFFD31510);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.0, vertical: sixteen),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cash icon
          Image.asset(
            'assets/cash.png',
            width: ten * 3.2,
            height: ten * 3.2,
          ),

          SizedBox(width: sixteen / 2),

          // Main text and time
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  mainText.length > 25
                      ? '${mainText.substring(0, 20)}..'
                      : mainText,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: forteen,
                    height: 1.71,
                    letterSpacing: 0,
                    color: Color(0xFF414346),
                  ),
                ),
                SizedBox(
                  height: 4,
                ),
                Text(
                  timeText,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: twelve,
                    height: 1.67,
                    letterSpacing: 0,
                    color: Color(0xFF414346),
                  ),
                ),
              ],
            ),
          ),

          // Price
          Text(
            price,
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: forteen,
              height: 1.43,
              letterSpacing: 0,
              color: priceColor,
            ),
          ),

          SizedBox(width: twelve),

          // Right arrow icon
          // Image.asset(
          //   'assets/right.png',
          //   width:twentyFour,
          //   height:twentyFour,
          // ),
        ],
      ),
    );
  }

  void _showPaymentSuccessDialog() {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(twenty),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: isTablet ? 500 : size.width * 0.9,
              maxHeight: size.height * 0.8,
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(isTablet ? twentyFour : twenty),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: isTablet ? 250 : 220,
                      child: Lottie.asset(
                        'assets/success.json',
                        fit: BoxFit.cover,
                        repeat: true,
                        animate: true,
                      ),
                    ),
                    Text(
                      'Wallet Deposit Successful!',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isTablet ? twentyFour : twenty,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: isTablet ? sixteen : twelve),
                    Text(
                      'Your wallet has been credited successfully. \nYou can now use this balance for your orders!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isTablet ? sixteen : forteen,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF66696D),
                      ),
                    ),
                    SizedBox(height: isTablet ? twentyFour : twenty),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop(); // Close dialog
                          // Refresh wallet transactions
                          context
                              .read<AccountBloc>()
                              .add(WalletTransactionListEvent(''));
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twelve),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? forteen : twelve,
                          ),
                        ),
                        child: Text(
                          'Continue',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: isTablet ? eighteen : sixteen,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showPaymentFailedDialog() {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(twenty),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: isTablet ? 500 : size.width * 0.9,
              maxHeight: size.height * 0.8,
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(isTablet ? twentyFour : twenty),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: isTablet ? 250 : 220,
                      child: Lottie.asset(
                        'assets/failed.json',
                        fit: BoxFit.cover,
                        repeat: true,
                        animate: true,
                      ),
                    ),
                    Text(
                      'Payment Failed!',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isTablet ? twentyFour : twenty,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: isTablet ? sixteen : twelve),
                    Text(
                      'Your payment could not be processed. Please try again or use a different payment method.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isTablet ? sixteen : forteen,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF66696D),
                      ),
                    ),
                    SizedBox(height: isTablet ? twentyFour : twenty),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop(); // Close dialog
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twelve),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? forteen : twelve,
                          ),
                        ),
                        child: Text(
                          'Try Again',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: isTablet ? eighteen : sixteen,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
